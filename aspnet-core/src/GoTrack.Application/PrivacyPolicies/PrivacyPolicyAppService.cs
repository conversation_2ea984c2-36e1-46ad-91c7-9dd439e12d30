using System.Threading.Tasks;
using GoTrack.Permissions;
using GoTrack.PrivacyPolicies.DTOs;
using Microsoft.AspNetCore.Authorization;

namespace GoTrack.PrivacyPolicies;

public class PrivacyPolicyAppService : GoTrackAppService,  IPrivacyPolicyAppService
{
    private readonly PrivacyPolicyManager _privacyPolicyManager;

    public PrivacyPolicyAppService(PrivacyPolicyManager privacyPolicyManager)
    {
        _privacyPolicyManager = privacyPolicyManager;
    }

    [Authorize(GoTrackPermissions.PrivacyPolicyGet)]
    public async Task<PrivacyPolicyDto?> GetLatestAsync()
    {
        var privacyPolicy = await _privacyPolicyManager.GetLastAsync();
        
        return ObjectMapper.Map<PrivacyPolicy?, PrivacyPolicyDto?>(privacyPolicy);
    }
    
    [Authorize(GoTrackPermissions.PrivacyPolicyUpdate)]
    public async Task<PrivacyPolicyDto> UpdateAsync(UpdatePrivacyPolicyDto updatePrivacyPolicyDto)
    {
        var  privacyPolicy = await _privacyPolicyManager.PutAsync(updatePrivacyPolicyDto.Content, updatePrivacyPolicyDto.ContentEn);
        return ObjectMapper.Map<PrivacyPolicy, PrivacyPolicyDto>(privacyPolicy);
    }
}