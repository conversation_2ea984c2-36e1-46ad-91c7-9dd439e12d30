using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.PrivacyPolicies;

public class PrivacyPolicy : FullAuditedAggregateRoot<Guid>
{
    public string Content { get; private set; }
    public string? ContentEn { get; private set; }
    public double Version { get; private set; }

    private PrivacyPolicy(){}

    public PrivacyPolicy(string content, double version, string? contentEn = null)
    {
        Content = content;
        ContentEn = contentEn;
        Version = version;
    }
}