using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DistributedLocking;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Identity;

namespace GoTrack.PrivacyPolicies;

public class PrivacyPolicyManager : DomainService
{
    private readonly IRepository<PrivacyPolicy, Guid> _privacyPolicyRepository;
    private readonly IAbpDistributedLock _distributedLock;
    private readonly IBackgroundJobManager _backgroundJobManager;
    public PrivacyPolicyManager(IRepository<PrivacyPolicy, Guid> privacyPolicyRepository, IAbpDistributedLock distributedLock, IBackgroundJobManager backgroundJobManager)
    {
        _privacyPolicyRepository = privacyPolicyRepository;
        _distributedLock = distributedLock;
        _backgroundJobManager = backgroundJobManager;
    }

    public async Task<PrivacyPolicy> PutAsync(string content, string? contentEn = null)
    {
        await using var handle = await _distributedLock.TryAcquireAsync("PrivacyPolicyUpdate");

        if (handle is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PrivacyPolicyUpdateLocked);
        }

        var queryable = await _privacyPolicyRepository.GetQueryableAsync();
        var lastPrivacyPolicy = await AsyncExecuter.FirstOrDefaultAsync(
            queryable.OrderByDescending(x => x.Version));

        var version = lastPrivacyPolicy?.Version ?? -1;
        var newPrivacyPolicy = new PrivacyPolicy(content, version + 1, contentEn);

        await _privacyPolicyRepository.InsertAsync(newPrivacyPolicy);

        await _backgroundJobManager.EnqueueAsync(
            new PrivacyPolicyUpdatedNotificationJobArgs()
        );

        return newPrivacyPolicy;
    }

    
    public async Task<PrivacyPolicy?> GetLastAsync()
    {
        var queryable = await _privacyPolicyRepository.GetQueryableAsync();
        queryable = queryable.OrderByDescending(x => x.Version);
        var lastPrivacyPolicy = await AsyncExecuter.FirstOrDefaultAsync(queryable);

        return lastPrivacyPolicy;
    }
}