using System;
using System.Globalization;
using System.Threading.Tasks;
using GoTrack.Identity;
using GoTrack.Mobile.PrivacyPolicies.DTOs;
using GoTrack.PrivacyPolicies;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace GoTrack.Mobile.PrivacyPolicies;

public class PrivacyPolicyAppService : GoTrackMobileAppService, IPrivacyPolicyAppService
{
    private readonly PrivacyPolicyManager _privacyPolicyManager;
    private readonly IRepository<IdentityUserProfile, Guid> _identityUserProfileRepository;
    private readonly IRepository<PrivacyPolicy, Guid> _privacyPolicyRepository;
    
    public PrivacyPolicyAppService(PrivacyPolicyManager privacyPolicyManager, IRepository<IdentityUserProfile, Guid> identityUserProfileRepository, IRepository<PrivacyPolicy, Guid> privacyPolicyRepository)
    {
        _privacyPolicyManager = privacyPolicyManager;
        _identityUserProfileRepository = identityUserProfileRepository;
        _privacyPolicyRepository = privacyPolicyRepository;
    }

    [Authorize]
    public virtual async Task<PrivacyPolicyDto?> GetLatestAsync()
    {
        var lastPrivacyPolicy =  await _privacyPolicyManager.GetLastAsync();

        if (lastPrivacyPolicy is null)
        {
            return null;
        }
        
        var userProfile = await _identityUserProfileRepository.GetAsync(CurrentUser.GetId());
        
        string content;
        if (CultureInfo.CurrentUICulture.Name == "en" && !string.IsNullOrWhiteSpace(lastPrivacyPolicy.ContentEn))
        {
            content = lastPrivacyPolicy.ContentEn;
        }
        else
        {
            content = lastPrivacyPolicy.Content;
        }

        return new PrivacyPolicyDto()
        {
            Content = content,
            Version = lastPrivacyPolicy.Version,
            IsAcceptable = userProfile.LastAcceptedPolicyVersion != null && 
                           userProfile.LastAcceptedPolicyVersion.Value.ToString() 
                           == lastPrivacyPolicy.Version.ToString()
        };
    }

    [Authorize]
    public virtual async Task AcceptAsync(AcceptPrivacyPolicyDto input)
    {
        if (await _privacyPolicyRepository.AnyAsync(x => x.Version.ToString() == input.Version.ToString()) is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidPolicyVersion);
        }
        
        var userProfile = await _identityUserProfileRepository.GetAsync(CurrentUser.GetId());
        userProfile.SetLastAcceptedPolicyVersion(input.Version);
        await _identityUserProfileRepository.UpdateAsync(userProfile);
    }
}