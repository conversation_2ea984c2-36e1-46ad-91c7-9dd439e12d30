using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.PrivacyPolicies.EntityConfiguration;

public class PrivacyPolicyEntityConfiguration : IEntityTypeConfiguration<PrivacyPolicy>
{
    public void Configure(EntityTypeBuilder<PrivacyPolicy> builder)
    {
        builder.ConfigureByConvention();

        builder.HasIndex(x => x.Version);

        builder.Property(x => x.Content)
            .HasMaxLength(8000);

        builder.Property(x => x.ContentEn)
            .HasMaxLength(8000);

        builder.ToGoTrackTable();
    }
}